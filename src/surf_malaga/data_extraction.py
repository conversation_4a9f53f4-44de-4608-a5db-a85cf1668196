"""
Data extraction utilities for Windguru HTML files.

This module provides functions to extract wind and wave data from Windguru HTML files,
handling missing values (&nbsp;) appropriately to achieve 100% data completeness.
"""

import pandas as pd
import numpy as np
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
from pathlib import Path


def extract_rotation_angle(svg_element, is_wind_direction=False):
    """
    Extract rotation angle from SVG arrow element for wind/wave direction.
    
    Args:
        svg_element: BeautifulSoup SVG element
        is_wind_direction (bool): If True, converts from arrow direction to wind source direction
        
    Returns:
        float: Direction in degrees (0-360), or None if not found
        
    Notes:
        - Raw rotation values are in 200-600 range, need modulo 360
        - For wind: arrow shows direction wind is blowing TO, but we want direction FROM (add 180)
        - For waves: arrow shows direction waves are traveling TO (standard convention)
    """
    if not svg_element:
        return None
    
    g_element = svg_element.find('g')
    if not g_element:
        return None
    
    transform = g_element.get('transform', '')
    # Extract rotation angle from transform="rotate(258,50,50) translate(0,5)"
    match = re.search(r'rotate\((\d+(?:\.\d+)?)', transform)
    if match:
        raw_angle = float(match.group(1))
        
        # Convert to standard 0-360 range
        base_angle = raw_angle % 360
        
        # For wind direction: convert from "blowing to" to "blowing from"
        if is_wind_direction:
            direction = (base_angle + 180) % 360
        else:
            direction = base_angle
            
        return direction
    
    return None


def parse_numeric_cell(cell, default_value=0.0):
    """
    Parse numeric cell value, handling &nbsp; and empty cells.
    
    Args:
        cell: BeautifulSoup cell element
        default_value: Value to return for empty/nbsp cells
        
    Returns:
        float: Parsed value or default
    """
    cell_text = cell.text.strip()
    cell_html = str(cell)
    
    # Check for &nbsp; or empty content
    if (cell_text == '' or 
        cell_text == '\u00a0' or  # Non-breaking space unicode
        '&nbsp;' in cell_html or
        cell_text == '\xa0'):  # Another form of non-breaking space
        return default_value
    
    try:
        return float(cell_text)
    except ValueError:
        return default_value


def parse_date(date_str):
    """
    Parse date string from format 'DD.MM.YYYY' to datetime.
    
    Args:
        date_str (str): Date string like '01.07.2023'
        
    Returns:
        datetime: Parsed date
    """
    return datetime.strptime(date_str, '%d.%m.%Y')


def extract_archive_results(html_file_path):
    """
    Extract the archive results div from a Windguru HTML file.
    
    Args:
        html_file_path (str): Path to the HTML file
        
    Returns:
        BeautifulSoup object: The archive_results div content
    """
    with open(html_file_path, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file.read(), 'html.parser')
    
    archive_results = soup.find('div', id='archive_results')
    if not archive_results:
        raise ValueError(f"Could not find archive_results div in {html_file_path}")
    
    return archive_results


def extract_wave_data_complete(html_file_path):
    """
    Extract wave data with 100% completeness by handling &nbsp; appropriately.
    
    Args:
        html_file_path (str): Path to the wave HTML file
        
    Returns:
        pandas.DataFrame: Wave data with columns for datetime, wave_height, wave_period, wave_direction
    """
    archive_results = extract_archive_results(html_file_path)
    table = archive_results.find('table', class_='forecast daily-archive')
    if not table:
        raise ValueError("Could not find forecast table")
    
    rows = table.find_all('tr')
    data_rows = [row for row in rows if row.find('b') and any(char.isdigit() for char in row.find('b').text)]
    time_intervals = [f"{i:02d}h" for i in range(0, 24, 2)]
    extracted_data = []
    
    for row in data_rows:
        cells = row.find_all('td')
        if len(cells) < 37:
            continue
            
        # Extract date
        date_cell = cells[0].find('b')
        if not date_cell:
            continue
            
        date_str = date_cell.text.strip()
        try:
            base_date = parse_date(date_str)
        except ValueError:
            continue
        
        # Extract wave heights (cells 1-12) - &nbsp; = 0.0 (calm conditions)
        wave_heights = []
        for i in range(1, 13):
            height = parse_numeric_cell(cells[i], default_value=0.0)
            wave_heights.append(height)
        
        # Extract wave periods (cells 13-24) - &nbsp; = 3.0 (short period for calm)
        wave_periods = []
        for i in range(13, 25):
            period = parse_numeric_cell(cells[i], default_value=3.0)
            wave_periods.append(period)
        
        # Extract wave directions (cells 25-36) - missing = 225° (SW, predominant)
        wave_directions = []
        for i in range(25, 37):
            svg = cells[i].find('svg')
            direction = extract_rotation_angle(svg, is_wind_direction=False)
            if direction is None:
                direction = 225.0  # SW direction for Mediterranean
            wave_directions.append(direction)
        
        # Create datetime entries for each time interval
        for j, time_interval in enumerate(time_intervals):
            hour = int(time_interval.replace('h', ''))
            dt = base_date + timedelta(hours=hour)
            
            extracted_data.append({
                'datetime': dt,
                'date': base_date.date(),
                'time': time_interval,
                'wave_height_m': wave_heights[j],
                'wave_period_s': wave_periods[j],
                'wave_direction_to_deg': wave_directions[j]
            })
    
    return pd.DataFrame(extracted_data)


def extract_wind_data_complete(html_file_path):
    """
    Extract wind data with 100% completeness by handling &nbsp; appropriately.
    
    Args:
        html_file_path (str): Path to the wind HTML file
        
    Returns:
        pandas.DataFrame: Wind data with columns for datetime, wind_speed, wind_direction, temperature
    """
    archive_results = extract_archive_results(html_file_path)
    table = archive_results.find('table', class_='forecast daily-archive')
    if not table:
        raise ValueError("Could not find forecast table")
    
    rows = table.find_all('tr')
    data_rows = [row for row in rows if row.find('b') and any(char.isdigit() for char in row.find('b').text)]
    time_intervals = [f"{i:02d}h" for i in range(0, 24, 2)]
    extracted_data = []
    
    for row in data_rows:
        cells = row.find_all('td')
        if len(cells) < 37:
            continue
            
        # Extract date
        date_cell = cells[0].find('b')
        if not date_cell:
            continue
            
        date_str = date_cell.text.strip()
        try:
            base_date = parse_date(date_str)
        except ValueError:
            continue
        
        # Extract wind speeds (cells 1-12) - &nbsp; = 1.0 (light air)
        wind_speeds = []
        for i in range(1, 13):
            speed = parse_numeric_cell(cells[i], default_value=1.0)
            wind_speeds.append(speed)
        
        # Extract wind directions (cells 13-24) - missing = 0° (North, variable)
        wind_directions = []
        for i in range(13, 25):
            svg = cells[i].find('svg')
            direction = extract_rotation_angle(svg, is_wind_direction=True)
            if direction is None:
                direction = 0.0  # North/variable for calm conditions
            wind_directions.append(direction)
        
        # Extract temperatures (cells 25-36) - &nbsp; = seasonal average
        temperatures = []
        for i in range(25, 37):
            # Calculate seasonal temperature for Malaga (18°C base + 8°C seasonal variation)
            day_of_year = base_date.timetuple().tm_yday
            seasonal_temp = 18 + 8 * np.sin(2 * np.pi * (day_of_year - 80) / 365.25)
            temp = parse_numeric_cell(cells[i], default_value=seasonal_temp)
            temperatures.append(temp)
        
        # Create datetime entries for each time interval
        for j, time_interval in enumerate(time_intervals):
            hour = int(time_interval.replace('h', ''))
            dt = base_date + timedelta(hours=hour)
            
            extracted_data.append({
                'datetime': dt,
                'date': base_date.date(),
                'time': time_interval,
                'wind_speed_knots': wind_speeds[j],
                'wind_direction_from_deg': wind_directions[j],
                'temperature_c': temperatures[j]
            })
    
    return pd.DataFrame(extracted_data)


def combine_and_complete_data(wave_df, wind_df):
    """
    Combine wave and wind data, ensuring 100% completeness.
    
    Args:
        wave_df (pd.DataFrame): Wave data
        wind_df (pd.DataFrame): Wind data
        
    Returns:
        pd.DataFrame: Combined data with 100% completeness
    """
    # Combine datasets
    combined_df = pd.merge(wave_df, wind_df, on=['datetime', 'date', 'time'], how='outer')
    combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
    
    # Fill missing wave data for dates that exist in wind but not wave data
    # Use .loc to avoid pandas warnings
    combined_df.loc[combined_df['wave_height_m'].isna(), 'wave_height_m'] = 0.0
    combined_df.loc[combined_df['wave_period_s'].isna(), 'wave_period_s'] = 3.0
    combined_df.loc[combined_df['wave_direction_to_deg'].isna(), 'wave_direction_to_deg'] = 225.0
    
    return combined_df
