import pandas as pd
import numpy as np
import sys
from datetime import datetime, timedelta
from pathlib import Path
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Set up paths
project_root = Path().resolve().parent
data_raw_path = project_root / 'data' / 'raw' / 'tides'
data_processed_path = project_root / 'data' / 'processed'

# Add src to path for imports
sys.path.append(str(project_root / 'src'))

# Import our extraction functions
from surf_malaga.tide_extraction import (
    extract_all_tide_data,
    interpolate_tide_data
)

print(f"Project root: {project_root}")
print(f"Raw tide data path: {data_raw_path}")
print(f"Processed data path: {data_processed_path}")

# List available PDF files
pdf_files = list(data_raw_path.glob('*.pdf'))
print(f"\nFound {len(pdf_files)} PDF files:")
for pdf_file in sorted(pdf_files):
    print(f"  - {pdf_file.name}")

# Extract tide data from all PDF files
print("Extracting tide data from PDF files...")
raw_tide_df = extract_all_tide_data(data_raw_path)

if not raw_tide_df.empty:
    print(f"\nExtracted {len(raw_tide_df)} tide records")
    print(f"Date range: {raw_tide_df['datetime'].min()} to {raw_tide_df['datetime'].max()}")
    print(f"\nFirst few records:")
    print(raw_tide_df.head(10))
    
    # Basic statistics
    print(f"\nTide height statistics:")
    print(raw_tide_df['tide_height_m'].describe())
else:
    print("No tide data extracted!")

raw_tide_df.describe()

if not raw_tide_df.empty:
    # Define date range to match wind/wave data
    start_date = datetime(2023, 7, 1)
    end_date = datetime(2025, 7, 11)
    
    print(f"Interpolating tide data from {start_date} to {end_date}")
    print("Using cubic spline interpolation to 2-hour intervals...")
    
    # Interpolate to 2-hour intervals
    interpolated_tide_df = interpolate_tide_data(raw_tide_df, start_date, end_date)
    
    print(f"\nInterpolated dataset shape: {interpolated_tide_df.shape}")
    print(f"Date range: {interpolated_tide_df['datetime'].min()} to {interpolated_tide_df['datetime'].max()}")
    print(f"\nFirst few interpolated records:")
    print(interpolated_tide_df.head(10))
    
    # Check for any missing values
    missing_values = interpolated_tide_df.isnull().sum().sum()
    print(f"\nMissing values in interpolated data: {missing_values}")
    
    if missing_values == 0:
        print("✓ 100% data completeness achieved!")
else:
    print("Cannot interpolate - no raw tide data available")

if 'interpolated_tide_df' in locals() and not interpolated_tide_df.empty:
    # Define output filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    tide_csv = data_processed_path / f"tide_data_{timestamp}.csv"
    tide_parquet = data_processed_path / f"tide_data_{timestamp}.parquet"
    
    # Save as CSV and Parquet
    print("Saving processed tide data...")
    interpolated_tide_df.to_csv(tide_csv, index=False)
    interpolated_tide_df.to_parquet(tide_parquet, index=False)
    
    print(f"✓ Tide data saved to: {tide_csv}")
    print(f"✓ Tide data saved to: {tide_parquet}")
    
    # Create 'latest' symlinks
    latest_csv = data_processed_path / "tide_data_latest.csv"
    latest_parquet = data_processed_path / "tide_data_latest.parquet"
    
    # Remove existing symlinks if they exist
    if latest_csv.is_symlink():
        latest_csv.unlink()
    if latest_parquet.is_symlink():
        latest_parquet.unlink()
    
    # Create new symlinks
    latest_csv.symlink_to(tide_csv.name)
    latest_parquet.symlink_to(tide_parquet.name)
    
    print(f"\n✓ Latest tide data accessible at:")
    print(f"  - {latest_csv}")
    print(f"  - {latest_parquet}")
    
    # Final summary
    print(f"\nFinal Tide Dataset Summary:")
    print(f"Total records: {len(interpolated_tide_df):,}")
    print(f"Date range: {interpolated_tide_df['datetime'].min()} to {interpolated_tide_df['datetime'].max()}")
    print(f"Time span: {(interpolated_tide_df['datetime'].max() - interpolated_tide_df['datetime'].min()).days} days")
    print(f"Data completeness: 100%")
    print(f"Time resolution: 2 hours")
    print(f"Tide range: {interpolated_tide_df['tide_height_m'].min():.2f}m to {interpolated_tide_df['tide_height_m'].max():.2f}m")
else:
    print("No interpolated tide data to save")