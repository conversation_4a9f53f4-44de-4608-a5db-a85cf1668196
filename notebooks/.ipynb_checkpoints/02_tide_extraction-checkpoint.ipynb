{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tide Data Extraction from PDFs\n", "\n", "This notebook extracts tide data from Sotogrande PDF files and processes it to align with the 2-hour intervals used in wind and wave data.\n", "\n", "## Data Sources\n", "- PDF files in `data/raw/tides/` containing Sotogrande tide tables\n", "- Complex nested tables with multiple months per page\n", "- High/low tide times and heights at irregular intervals\n", "\n", "## Processing Steps\n", "1. Extract raw tide data from PDF tables using pdfplumber\n", "2. Parse complex nested table structure\n", "3. Convert to structured DataFrame with datetime and tide height\n", "4. Use interpolation to align with 2-hour intervals (00h, 02h, 04h, etc.)\n", "5. Output as CSV for easy combination with wind/wave data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import sys\n", "from datetime import datetime, timedelta\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up paths\n", "project_root = Path().resolve().parent\n", "data_raw_path = project_root / 'data' / 'raw' / 'tides'\n", "data_processed_path = project_root / 'data' / 'processed'\n", "\n", "# Add src to path for imports\n", "sys.path.append(str(project_root / 'src'))\n", "\n", "# Import our extraction functions\n", "from surf_malaga.tide_extraction import (\n", "    extract_all_tide_data,\n", "    interpolate_tide_data\n", ")\n", "\n", "print(f\"Project root: {project_root}\")\n", "print(f\"Raw tide data path: {data_raw_path}\")\n", "print(f\"Processed data path: {data_processed_path}\")\n", "\n", "# List available PDF files\n", "pdf_files = list(data_raw_path.glob('*.pdf'))\n", "print(f\"\\nFound {len(pdf_files)} PDF files:\")\n", "for pdf_file in sorted(pdf_files):\n", "    print(f\"  - {pdf_file.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Raw Tide Data\n", "\n", "Extract tide data from all PDF files using the extraction functions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract tide data from all PDF files\n", "print(\"Extracting tide data from PDF files...\")\n", "raw_tide_df = extract_all_tide_data(data_raw_path)\n", "\n", "if not raw_tide_df.empty:\n", "    print(f\"\\nExtracted {len(raw_tide_df)} tide records\")\n", "    print(f\"Date range: {raw_tide_df['datetime'].min()} to {raw_tide_df['datetime'].max()}\")\n", "    print(f\"\\nFirst few records:\")\n", "    print(raw_tide_df.head(10))\n", "    \n", "    # Basic statistics\n", "    print(f\"\\nTide height statistics:\")\n", "    print(raw_tide_df['tide_height_m'].describe())\n", "else:\n", "    print(\"No tide data extracted!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interpolate to 2-Hour Intervals\n", "\n", "Align tide data with the 2-hour intervals used in wind and wave data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not raw_tide_df.empty:\n", "    # Define date range to match wind/wave data\n", "    start_date = datetime(2023, 7, 1)\n", "    end_date = datetime(2025, 7, 11)\n", "    \n", "    print(f\"Interpolating tide data from {start_date} to {end_date}\")\n", "    print(\"Using cubic spline interpolation to 2-hour intervals...\")\n", "    \n", "    # Interpolate to 2-hour intervals\n", "    interpolated_tide_df = interpolate_tide_data(raw_tide_df, start_date, end_date)\n", "    \n", "    print(f\"\\nInterpolated dataset shape: {interpolated_tide_df.shape}\")\n", "    print(f\"Date range: {interpolated_tide_df['datetime'].min()} to {interpolated_tide_df['datetime'].max()}\")\n", "    print(f\"\\nFirst few interpolated records:\")\n", "    print(interpolated_tide_df.head(10))\n", "    \n", "    # Check for any missing values\n", "    missing_values = interpolated_tide_df.isnull().sum().sum()\n", "    print(f\"\\nMissing values in interpolated data: {missing_values}\")\n", "    \n", "    if missing_values == 0:\n", "        print(\"✓ 100% data completeness achieved!\")\n", "else:\n", "    print(\"Cannot interpolate - no raw tide data available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Processed Tide Data\n", "\n", "Save the interpolated tide data as CSV for easy combination with wind/wave data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'interpolated_tide_df' in locals() and not interpolated_tide_df.empty:\n", "    # Define output filename with timestamp\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    tide_csv = data_processed_path / f\"tide_data_{timestamp}.csv\"\n", "    tide_parquet = data_processed_path / f\"tide_data_{timestamp}.parquet\"\n", "    \n", "    # Save as CSV and Parquet\n", "    print(\"Saving processed tide data...\")\n", "    interpolated_tide_df.to_csv(tide_csv, index=False)\n", "    interpolated_tide_df.to_parquet(tide_parquet, index=False)\n", "    \n", "    print(f\"✓ Tide data saved to: {tide_csv}\")\n", "    print(f\"✓ Tide data saved to: {tide_parquet}\")\n", "    \n", "    # Create 'latest' symlinks\n", "    latest_csv = data_processed_path / \"tide_data_latest.csv\"\n", "    latest_parquet = data_processed_path / \"tide_data_latest.parquet\"\n", "    \n", "    # Remove existing symlinks if they exist\n", "    if latest_csv.is_symlink():\n", "        latest_csv.unlink()\n", "    if latest_parquet.is_symlink():\n", "        latest_parquet.unlink()\n", "    \n", "    # Create new symlinks\n", "    latest_csv.symlink_to(tide_csv.name)\n", "    latest_parquet.symlink_to(tide_parquet.name)\n", "    \n", "    print(f\"\\n✓ Latest tide data accessible at:\")\n", "    print(f\"  - {latest_csv}\")\n", "    print(f\"  - {latest_parquet}\")\n", "    \n", "    # Final summary\n", "    print(f\"\\nFinal Tide Dataset Summary:\")\n", "    print(f\"Total records: {len(interpolated_tide_df):,}\")\n", "    print(f\"Date range: {interpolated_tide_df['datetime'].min()} to {interpolated_tide_df['datetime'].max()}\")\n", "    print(f\"Time span: {(interpolated_tide_df['datetime'].max() - interpolated_tide_df['datetime'].min()).days} days\")\n", "    print(f\"Data completeness: 100%\")\n", "    print(f\"Time resolution: 2 hours\")\n", "    print(f\"Tide range: {interpolated_tide_df['tide_height_m'].min():.2f}m to {interpolated_tide_df['tide_height_m'].max():.2f}m\")\n", "else:\n", "    print(\"No interpolated tide data to save\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 4}