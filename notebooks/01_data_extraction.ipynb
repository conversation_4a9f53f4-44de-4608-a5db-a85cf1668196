import pandas as pd
import sys
from datetime import datetime
from pathlib import Path

# Set up paths
project_root = Path().resolve().parent
data_raw_path = project_root / 'data' / 'raw'
data_processed_path = project_root / 'data' / 'processed'

# Add src to path for imports
sys.path.append(str(project_root / 'src'))

# Import our extraction functions
from surf_malaga.data_extraction import (
    extract_wave_data_complete,
    extract_wind_data_complete
)

# Ensure processed data directory exists
data_processed_path.mkdir(parents=True, exist_ok=True)

print(f"Project root: {project_root}")
print(f"Raw data path: {data_raw_path}")
print(f"Processed data path: {data_processed_path}")

# Extract wave data using the complete extraction function
wave_file = data_raw_path / 'Windguru_El_Palo_Wave_2h_2023-07-01_2025-07-11.html'
print(f"Extracting wave data from: {wave_file}")

wave_df = extract_wave_data_complete(wave_file)
print(f"Extracted {len(wave_df)} wave data records")
print(f"Date range: {wave_df['datetime'].min()} to {wave_df['datetime'].max()}")
print(f"Missing values in wave data: {wave_df.isnull().sum().sum()}")
wave_df.head()

# Extract wind data using the complete extraction function
wind_file = data_raw_path / 'Windguru_El_Palo_Wind_2h_2023-07-01_2025-07-11.html'
print(f"Extracting wind data from: {wind_file}")

wind_df = extract_wind_data_complete(wind_file)
print(f"Extracted {len(wind_df)} wind data records")
print(f"Date range: {wind_df['datetime'].min()} to {wind_df['datetime'].max()}")
print(f"Missing values in wind data: {wind_df.isnull().sum().sum()}")
wind_df.head()

# Generate timestamp for filenames
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

# Save individual datasets as Parquet (recommended for analysis)
wave_parquet = data_processed_path / f"wave_data_{timestamp}.parquet"
wind_parquet = data_processed_path / f"wind_data_{timestamp}.parquet"

print("Saving individual datasets as Parquet files...")
wave_df.to_parquet(wave_parquet, index=False)
wind_df.to_parquet(wind_parquet, index=False)

print(f"✓ Wave data saved to: {wave_parquet}")
print(f"✓ Wind data saved to: {wind_parquet}")

# Save individual datasets as CSV (for compatibility)
wave_csv = data_processed_path / f"wave_data_{timestamp}.csv"
wind_csv = data_processed_path / f"wind_data_{timestamp}.csv"

print("\nSaving individual datasets as CSV files...")
wave_df.to_csv(wave_csv, index=False)
wind_df.to_csv(wind_csv, index=False)

print(f"✓ Wave data saved to: {wave_csv}")
print(f"✓ Wind data saved to: {wind_csv}")

print(f"\nIndividual datasets saved to: {data_processed_path}")
print("\n📝 Next step: Run the '03_data_combining.ipynb' notebook to combine wind, wave, and tide data.")

# Final extraction summary
print("🌊 WIND & WAVE DATA EXTRACTION COMPLETED! 🌊")
print("=" * 50)

print(f"\nWave Dataset Summary:")
print(f"Total records: {len(wave_df):,}")
print(f"Date range: {wave_df['datetime'].min()} to {wave_df['datetime'].max()}")
print(f"Missing values: {wave_df.isnull().sum().sum()}")

print(f"\nWind Dataset Summary:")
print(f"Total records: {len(wind_df):,}")
print(f"Date range: {wind_df['datetime'].min()} to {wind_df['datetime'].max()}")
print(f"Missing values: {wind_df.isnull().sum().sum()}")

print(f"\nFiles saved to: {data_processed_path}")
print(f"\n📝 Next step: Run '03_data_combining.ipynb' to combine with tide data for complete dataset.")