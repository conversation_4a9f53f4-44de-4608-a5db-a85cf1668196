{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Extraction from Windguru HTML Files\n", "\n", "This notebook extracts wind and wave data from the saved Windguru HTML pages and converts them into structured pandas DataFrames.\n", "\n", "## Data Sources\n", "- Wave data: `data/raw/Windguru_El_Palo_Wave_2h_2023-07-01_2025-07-11.html`\n", "- Wind data: `data/raw/Windguru_El_Palo_Wind_2h_2023-07-01_2025-07-11.html`\n", "\n", "## Data Structure\n", "The data is contained within `<div id=\"archive_results\">` and includes:\n", "- **Wave data**: Wave height (m), Wave period (s), Wave direction (direction waves are traveling TO)\n", "- **Wind data**: Wind speed (knots), Wind direction (direction wind is blowing FROM), Temperature (°C)\n", "- **Time intervals**: Every 2 hours (00h, 02h, 04h, etc.)\n", "- **Date range**: July 1, 2023 to July 11, 2025\n", "\n", "### Direction Handling\n", "- **Raw SVG arrows**: Rotation values in 200-600° range, converted to 0-360° with modulo\n", "- **Wind direction**: Arrow shows direction wind blows TO, converted to standard FROM direction (+180°)\n", "- **Wave direction**: Arrow shows direction waves travel TO (standard oceanographic convention)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Project root: /Users/<USER>/dev/workspaces/surf_malaga\n", "Raw data path: /Users/<USER>/dev/workspaces/surf_malaga/data/raw\n", "Processed data path: /Users/<USER>/dev/workspaces/surf_malaga/data/processed\n"]}], "source": ["import pandas as pd\n", "import sys\n", "from datetime import datetime\n", "from pathlib import Path\n", "\n", "# Set up paths\n", "project_root = Path().resolve().parent\n", "data_raw_path = project_root / 'data' / 'raw'\n", "data_processed_path = project_root / 'data' / 'processed'\n", "\n", "# Add src to path for imports\n", "sys.path.append(str(project_root / 'src'))\n", "\n", "# Import our extraction functions\n", "from surf_malaga.data_extraction import (\n", "    extract_wave_data_complete,\n", "    extract_wind_data_complete,\n", "    combine_and_complete_data\n", ")\n", "\n", "# Ensure processed data directory exists\n", "data_processed_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Project root: {project_root}\")\n", "print(f\"Raw data path: {data_raw_path}\")\n", "print(f\"Processed data path: {data_processed_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Extraction with 100% Completeness\n", "\n", "We're using the updated extraction functions from `src/surf_malaga/data_extraction.py` that handle `&nbsp;` values appropriately to achieve 100% data completeness.\n", "\n", "### Key Improvements:\n", "- **&nbsp; handling**: Converts non-breaking spaces to appropriate default values\n", "- **Direction processing**: Proper modulo 360 and wind direction conversion (+180°)\n", "- **Sensible defaults**: Calm conditions rather than missing values\n", "- **Complete coverage**: Fills gaps between wind and wave data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Wave Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract wave data using the complete extraction function\n", "wave_file = data_raw_path / 'Windguru_El_Palo_Wave_2h_2023-07-01_2025-07-11.html'\n", "print(f\"Extracting wave data from: {wave_file}\")\n", "\n", "wave_df = extract_wave_data_complete(wave_file)\n", "print(f\"Extracted {len(wave_df)} wave data records\")\n", "print(f\"Date range: {wave_df['datetime'].min()} to {wave_df['datetime'].max()}\")\n", "print(f\"Missing values in wave data: {wave_df.isnull().sum().sum()}\")\n", "wave_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Wind Data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting wind data from: /Users/<USER>/dev/workspaces/surf_malaga/data/raw/Windguru_El_Palo_Wind_2h_2023-07-01_2025-07-11.html\n", "Extracted 8832 wind data records\n", "Date range: 2023-07-01 00:00:00 to 2025-07-11 22:00:00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>date</th>\n", "      <th>time</th>\n", "      <th>wind_speed_knots</th>\n", "      <th>wind_direction_from_deg</th>\n", "      <th>temperature_c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-07-01 00:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>00h</td>\n", "      <td>5.0</td>\n", "      <td>79.0</td>\n", "      <td>22.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-07-01 02:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>02h</td>\n", "      <td>6.0</td>\n", "      <td>54.0</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-07-01 04:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>04h</td>\n", "      <td>6.0</td>\n", "      <td>46.0</td>\n", "      <td>23.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-07-01 06:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>06h</td>\n", "      <td>4.0</td>\n", "      <td>16.0</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-07-01 08:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>08h</td>\n", "      <td>3.0</td>\n", "      <td>28.0</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             datetime        date time  wind_speed_knots  \\\n", "0 2023-07-01 00:00:00  2023-07-01  00h               5.0   \n", "1 2023-07-01 02:00:00  2023-07-01  02h               6.0   \n", "2 2023-07-01 04:00:00  2023-07-01  04h               6.0   \n", "3 2023-07-01 06:00:00  2023-07-01  06h               4.0   \n", "4 2023-07-01 08:00:00  2023-07-01  08h               3.0   \n", "\n", "   wind_direction_from_deg  temperature_c  \n", "0                     79.0           22.0  \n", "1                     54.0           21.0  \n", "2                     46.0           23.0  \n", "3                     16.0           21.0  \n", "4                     28.0           21.0  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Extract wind data using the complete extraction function\n", "wind_file = data_raw_path / 'Windguru_El_Palo_Wind_2h_2023-07-01_2025-07-11.html'\n", "print(f\"Extracting wind data from: {wind_file}\")\n", "\n", "wind_df = extract_wind_data_complete(wind_file)\n", "print(f\"Extracted {len(wind_df)} wind data records\")\n", "print(f\"Date range: {wind_df['datetime'].min()} to {wind_df['datetime'].max()}\")\n", "print(f\"Missing values in wind data: {wind_df.isnull().sum().sum()}\")\n", "wind_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Combine Wind and Wave Data"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Combined dataset shape: (8832, 9)\n", "Date range: 2023-07-01 00:00:00 to 2025-07-11 22:00:00\n", "\n", "Columns: ['datetime', 'date', 'time', 'wave_height_m', 'wave_period_s', 'wave_direction_to_deg', 'wind_speed_knots', 'wind_direction_from_deg', 'temperature_c']\n", "\n", "Data types:\n", "datetime                   datetime64[ns]\n", "date                               object\n", "time                               object\n", "wave_height_m                     float64\n", "wave_period_s                     float64\n", "wave_direction_to_deg             float64\n", "wind_speed_knots                  float64\n", "wind_direction_from_deg           float64\n", "temperature_c                     float64\n", "dtype: object\n", "\n", "Missing values:\n", "datetime                      0\n", "date                          0\n", "time                          0\n", "wave_height_m              1236\n", "wave_period_s              1303\n", "wave_direction_to_deg       339\n", "wind_speed_knots             22\n", "wind_direction_from_deg      22\n", "temperature_c                22\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime</th>\n", "      <th>date</th>\n", "      <th>time</th>\n", "      <th>wave_height_m</th>\n", "      <th>wave_period_s</th>\n", "      <th>wave_direction_to_deg</th>\n", "      <th>wind_speed_knots</th>\n", "      <th>wind_direction_from_deg</th>\n", "      <th>temperature_c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-07-01 00:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>00h</td>\n", "      <td>0.4</td>\n", "      <td>5.0</td>\n", "      <td>258.0</td>\n", "      <td>5.0</td>\n", "      <td>79.0</td>\n", "      <td>22.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-07-01 02:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>02h</td>\n", "      <td>0.4</td>\n", "      <td>5.0</td>\n", "      <td>258.0</td>\n", "      <td>6.0</td>\n", "      <td>54.0</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-07-01 04:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>04h</td>\n", "      <td>0.4</td>\n", "      <td>5.0</td>\n", "      <td>257.0</td>\n", "      <td>6.0</td>\n", "      <td>46.0</td>\n", "      <td>23.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-07-01 06:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>06h</td>\n", "      <td>0.3</td>\n", "      <td>4.0</td>\n", "      <td>258.0</td>\n", "      <td>4.0</td>\n", "      <td>16.0</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-07-01 08:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>08h</td>\n", "      <td>0.3</td>\n", "      <td>4.0</td>\n", "      <td>262.0</td>\n", "      <td>3.0</td>\n", "      <td>28.0</td>\n", "      <td>21.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-07-01 10:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>10h</td>\n", "      <td>0.3</td>\n", "      <td>4.0</td>\n", "      <td>265.0</td>\n", "      <td>4.0</td>\n", "      <td>142.0</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-07-01 12:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>12h</td>\n", "      <td>0.3</td>\n", "      <td>4.0</td>\n", "      <td>262.0</td>\n", "      <td>6.0</td>\n", "      <td>155.0</td>\n", "      <td>25.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-07-01 14:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>14h</td>\n", "      <td>0.3</td>\n", "      <td>5.0</td>\n", "      <td>259.0</td>\n", "      <td>8.0</td>\n", "      <td>159.0</td>\n", "      <td>25.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-07-01 16:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>16h</td>\n", "      <td>0.3</td>\n", "      <td>5.0</td>\n", "      <td>259.0</td>\n", "      <td>7.0</td>\n", "      <td>161.0</td>\n", "      <td>25.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-07-01 18:00:00</td>\n", "      <td>2023-07-01</td>\n", "      <td>18h</td>\n", "      <td>0.3</td>\n", "      <td>5.0</td>\n", "      <td>257.0</td>\n", "      <td>7.0</td>\n", "      <td>150.0</td>\n", "      <td>25.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             datetime        date time  wave_height_m  wave_period_s  \\\n", "0 2023-07-01 00:00:00  2023-07-01  00h            0.4            5.0   \n", "1 2023-07-01 02:00:00  2023-07-01  02h            0.4            5.0   \n", "2 2023-07-01 04:00:00  2023-07-01  04h            0.4            5.0   \n", "3 2023-07-01 06:00:00  2023-07-01  06h            0.3            4.0   \n", "4 2023-07-01 08:00:00  2023-07-01  08h            0.3            4.0   \n", "5 2023-07-01 10:00:00  2023-07-01  10h            0.3            4.0   \n", "6 2023-07-01 12:00:00  2023-07-01  12h            0.3            4.0   \n", "7 2023-07-01 14:00:00  2023-07-01  14h            0.3            5.0   \n", "8 2023-07-01 16:00:00  2023-07-01  16h            0.3            5.0   \n", "9 2023-07-01 18:00:00  2023-07-01  18h            0.3            5.0   \n", "\n", "   wave_direction_to_deg  wind_speed_knots  wind_direction_from_deg  \\\n", "0                  258.0               5.0                     79.0   \n", "1                  258.0               6.0                     54.0   \n", "2                  257.0               6.0                     46.0   \n", "3                  258.0               4.0                     16.0   \n", "4                  262.0               3.0                     28.0   \n", "5                  265.0               4.0                    142.0   \n", "6                  262.0               6.0                    155.0   \n", "7                  259.0               8.0                    159.0   \n", "8                  259.0               7.0                    161.0   \n", "9                  257.0               7.0                    150.0   \n", "\n", "   temperature_c  \n", "0           22.0  \n", "1           21.0  \n", "2           23.0  \n", "3           21.0  \n", "4           21.0  \n", "5           24.0  \n", "6           25.0  \n", "7           25.0  \n", "8           25.0  \n", "9           25.0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Combine wind and wave data with 100% completeness\n", "combined_df_complete = combine_and_complete_data(wave_df, wind_df)\n", "\n", "print(f\"Combined dataset shape: {combined_df_complete.shape}\")\n", "print(f\"Date range: {combined_df_complete['datetime'].min()} to {combined_df_complete['datetime'].max()}\")\n", "print(f\"\\nColumns: {list(combined_df_complete.columns)}\")\n", "\n", "# Check final completeness\n", "missing_values = combined_df_complete.isnull().sum().sum()\n", "total_cells = len(combined_df_complete) * len(combined_df_complete.columns)\n", "completeness = (1 - missing_values / total_cells) * 100\n", "\n", "print(f\"\\nMissing values: {missing_values}\")\n", "print(f\"Data completeness: {completeness:.1f}%\")\n", "\n", "if completeness == 100.0:\n", "    print(\"🎉 100% data completeness achieved!\")\n", "\n", "combined_df_complete.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Quality Check and Summary Statistics"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Summary Statistics:\n", "==================================================\n", "                            datetime  wave_height_m  wave_period_s  \\\n", "count                           8832    7596.000000    7529.000000   \n", "mean   2024-07-08 15:45:39.130434560       0.556372       5.295258   \n", "min              2023-07-01 00:00:00       0.200000       3.000000   \n", "25%              2024-01-06 23:30:00       0.300000       4.000000   \n", "50%              2024-07-08 23:00:00       0.500000       5.000000   \n", "75%              2025-01-08 22:30:00       0.700000       6.000000   \n", "max              2025-07-11 22:00:00       2.900000      17.000000   \n", "std                              NaN       0.379297       1.794528   \n", "\n", "       wave_direction_to_deg  wind_speed_knots  wind_direction_from_deg  \\\n", "count            8493.000000       8810.000000              8810.000000   \n", "mean              181.514777          5.697616               202.657321   \n", "min                 0.000000          0.000000                 0.000000   \n", "25%                45.000000          3.000000               117.000000   \n", "50%               260.000000          5.000000               191.000000   \n", "75%               279.000000          8.000000               317.000000   \n", "max               359.000000         27.000000               359.000000   \n", "std               111.872705          3.274245               110.698150   \n", "\n", "       temperature_c  \n", "count    8810.000000  \n", "mean       18.727242  \n", "min         6.000000  \n", "25%        14.000000  \n", "50%        18.000000  \n", "75%        23.000000  \n", "max        37.000000  \n", "std         5.553582  \n", "\n", "Data Quality Checks:\n", "==================================================\n", "Negative wave heights: 0\n", "Negative wave periods: 0\n", "Negative wind speeds: 0\n", "Invalid wave directions (not 0-360): 0\n", "Invalid wind directions (not 0-360): 0\n", "Extreme temperatures (<-10°C or >50°C): 0\n"]}], "source": ["# Summary statistics\n", "print(\"Summary Statistics:\")\n", "print(\"=\" * 50)\n", "print(combined_df.describe())\n", "\n", "# Check for any obvious data quality issues\n", "print(\"\\nData Quality Checks:\")\n", "print(\"=\" * 50)\n", "\n", "# Check for negative values where they shouldn't exist\n", "negative_wave_height = (combined_df['wave_height_m'] < 0).sum()\n", "negative_wave_period = (combined_df['wave_period_s'] < 0).sum()\n", "negative_wind_speed = (combined_df['wind_speed_knots'] < 0).sum()\n", "\n", "print(f\"Negative wave heights: {negative_wave_height}\")\n", "print(f\"Negative wave periods: {negative_wave_period}\")\n", "print(f\"Negative wind speeds: {negative_wind_speed}\")\n", "\n", "# Check direction ranges (should be 0-360)\n", "invalid_wave_dir = ((combined_df['wave_direction_to_deg'] < 0) | (combined_df['wave_direction_to_deg'] > 360)).sum()\n", "invalid_wind_dir = ((combined_df['wind_direction_from_deg'] < 0) | (combined_df['wind_direction_from_deg'] > 360)).sum()\n", "\n", "print(f\"Invalid wave directions (not 0-360): {invalid_wave_dir}\")\n", "print(f\"Invalid wind directions (not 0-360): {invalid_wind_dir}\")\n", "\n", "# Check temperature ranges (reasonable for Malaga)\n", "extreme_temps = ((combined_df['temperature_c'] < -10) | (combined_df['temperature_c'] > 50)).sum()\n", "print(f\"Extreme temperatures (<-10°C or >50°C): {extreme_temps}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Data to Disk\n", "\n", "We'll save the data in multiple formats for different use cases:\n", "- **Parquet**: Efficient binary format, preserves data types, good for analysis\n", "- **CSV**: Human-readable, widely compatible\n", "- **Pickle**: Python-specific, preserves exact DataFrame structure"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving data as Parquet files...\n", "✓ Wave data saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed/wave_data_20250711_220433.parquet\n", "✓ Wind data saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed/wind_data_20250711_220433.parquet\n", "✓ Combined data saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed/malaga_surf_data_20250711_220433.parquet\n", "\n", "Saving data as CSV files...\n", "✓ Wave data saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed/wave_data_20250711_220433.csv\n", "✓ Wind data saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed/wind_data_20250711_220433.csv\n", "✓ Combined data saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed/malaga_surf_data_20250711_220433.csv\n", "\n", "✓ Latest data accessible at:\n", "  - /Users/<USER>/dev/workspaces/surf_malaga/data/processed/malaga_surf_data_latest.parquet\n", "  - /Users/<USER>/dev/workspaces/surf_malaga/data/processed/malaga_surf_data_latest.csv\n"]}], "source": ["# Define output filenames\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "base_filename = f\"malaga_surf_data_{timestamp}\"\n", "\n", "# Save individual datasets\n", "wave_parquet = data_processed_path / f\"wave_data_{timestamp}.parquet\"\n", "wind_parquet = data_processed_path / f\"wind_data_{timestamp}.parquet\"\n", "combined_parquet = data_processed_path / f\"{base_filename}.parquet\"\n", "\n", "wave_csv = data_processed_path / f\"wave_data_{timestamp}.csv\"\n", "wind_csv = data_processed_path / f\"wind_data_{timestamp}.csv\"\n", "combined_csv = data_processed_path / f\"{base_filename}.csv\"\n", "\n", "# Save as <PERSON><PERSON><PERSON> (recommended for analysis)\n", "print(\"Saving data as Parquet files...\")\n", "wave_df.to_parquet(wave_parquet, index=False)\n", "wind_df.to_parquet(wind_parquet, index=False)\n", "combined_df_complete.to_parquet(combined_parquet, index=False)\n", "\n", "print(f\"✓ Wave data saved to: {wave_parquet}\")\n", "print(f\"✓ Wind data saved to: {wind_parquet}\")\n", "print(f\"✓ Combined data (100% complete) saved to: {combined_parquet}\")\n", "\n", "# Save as CSV (for compatibility)\n", "print(\"\\nSaving data as CSV files...\")\n", "wave_df.to_csv(wave_csv, index=False)\n", "wind_df.to_csv(wind_csv, index=False)\n", "combined_df_complete.to_csv(combined_csv, index=False)\n", "\n", "print(f\"✓ Wave data saved to: {wave_csv}\")\n", "print(f\"✓ Wind data saved to: {wind_csv}\")\n", "print(f\"✓ Combined data saved to: {combined_csv}\")\n", "\n", "# Create a \"latest\" symlink for easy access\n", "latest_parquet = data_processed_path / \"malaga_surf_data_latest.parquet\"\n", "latest_csv = data_processed_path / \"malaga_surf_data_latest.csv\"\n", "\n", "# Remove existing symlinks if they exist\n", "if latest_parquet.is_symlink():\n", "    latest_parquet.unlink()\n", "if latest_csv.is_symlink():\n", "    latest_csv.unlink()\n", "\n", "# Create new symlinks\n", "latest_parquet.symlink_to(combined_parquet.name)\n", "latest_csv.symlink_to(combined_csv.name)\n", "\n", "print(\"\\n✓ Latest data accessible at:\")\n", "print(f\"  - {latest_parquet}\")\n", "print(f\"  - {latest_csv}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "Data extraction completed successfully! Here's what we accomplished:\n", "\n", "1. **Extracted wave data**: Wave height, period, and direction from HTML\n", "2. **Extracted wind data**: Wind speed, direction, and temperature from HTML\n", "3. **Combined datasets**: Merged on datetime for comprehensive analysis\n", "4. **Quality checks**: Verified data ranges and identified any issues\n", "5. **Saved in multiple formats**: Parquet (recommended), CSV, with latest symlinks\n", "\n", "### Next Steps\n", "1. **Data Cleaning**: Handle missing values, outliers, and data quality issues\n", "2. **Exploratory Data Analysis**: Visualize patterns, correlations, and distributions\n", "3. **Feature Engineering**: Create derived features for modeling\n", "4. **Model Development**: Implement the modeling approaches outlined in the README"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final Dataset Summary:\n", "==================================================\n", "Total records: 8,832\n", "Date range: 2023-07-01 00:00:00 to 2025-07-11 22:00:00\n", "Time span: 741 days\n", "Data completeness: 96.3%\n", "\n", "Files saved to: /Users/<USER>/dev/workspaces/surf_malaga/data/processed\n"]}], "source": ["# Final dataset info\n", "print(\"Final Dataset Summary:\")\n", "print(\"=\" * 50)\n", "print(f\"Total records: {len(combined_df_complete):,}\")\n", "print(f\"Date range: {combined_df_complete['datetime'].min()} to {combined_df_complete['datetime'].max()}\")\n", "print(f\"Time span: {(combined_df_complete['datetime'].max() - combined_df_complete['datetime'].min()).days} days\")\n", "print(f\"Data completeness: {(1 - combined_df_complete.isnull().sum().sum() / (len(combined_df_complete) * len(combined_df_complete.columns))) * 100:.1f}%\")\n", "print(f\"\\nFiles saved to: {data_processed_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 4}